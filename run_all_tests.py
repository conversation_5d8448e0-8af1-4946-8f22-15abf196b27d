#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新浪财经网站自动化测试套件主运行器
统一执行所有自动化测试用例
"""

import unittest
import sys
import time
from datetime import datetime
import os

# 导入测试模块
from test_sina_finance_automation import SinaFinanceAutomationTest
from test_user_login_automation import UserLoginAutomationTest  
from test_news_functionality import NewsAutomationTest


class TestSuiteRunner:
    """测试套件运行器"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.total_results = {
            'tests_run': 0,
            'failures': 0,
            'errors': 0,
            'success': 0
        }
    
    def print_header(self):
        """打印测试开始信息"""
        print("=" * 80)
        print("新浪财经网站自动化测试套件")
        print("=" * 80)
        print(f"测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"测试目标: https://finance.sina.com.cn/")
        print("=" * 80)
    
    def print_footer(self):
        """打印测试结束信息"""
        duration = self.end_time - self.start_time
        print("\n" + "=" * 80)
        print("测试套件执行完成")
        print("=" * 80)
        print(f"测试结束时间: {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总执行时间: {duration.total_seconds():.2f} 秒")
        print("\n测试结果汇总:")
        print(f"  总测试用例数: {self.total_results['tests_run']}")
        print(f"  成功: {self.total_results['success']}")
        print(f"  失败: {self.total_results['failures']}")
        print(f"  错误: {self.total_results['errors']}")
        
        success_rate = (self.total_results['success'] / self.total_results['tests_run'] * 100) if self.total_results['tests_run'] > 0 else 0
        print(f"  成功率: {success_rate:.1f}%")
        print("=" * 80)
    
    def run_test_class(self, test_class, test_name):
        """运行单个测试类"""
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        # 创建测试套件
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
        result = runner.run(suite)
        
        # 统计结果
        self.total_results['tests_run'] += result.testsRun
        self.total_results['failures'] += len(result.failures)
        self.total_results['errors'] += len(result.errors)
        self.total_results['success'] += (result.testsRun - len(result.failures) - len(result.errors))
        
        # 打印单个测试类结果
        print(f"\n{test_name} 结果:")
        print(f"  测试数: {result.testsRun}")
        print(f"  成功: {result.testsRun - len(result.failures) - len(result.errors)}")
        print(f"  失败: {len(result.failures)}")
        print(f"  错误: {len(result.errors)}")
        
        if result.failures:
            print("  失败详情:")
            for test, traceback in result.failures:
                print(f"    - {test}: {traceback.split('AssertionError:')[-1].strip() if 'AssertionError:' in traceback else 'Unknown failure'}")
        
        if result.errors:
            print("  错误详情:")
            for test, traceback in result.errors:
                print(f"    - {test}: {traceback.split('Exception:')[-1].strip() if 'Exception:' in traceback else 'Unknown error'}")
        
        return result
    
    def run_all_tests(self):
        """运行所有测试"""
        self.start_time = datetime.now()
        self.print_header()
        
        # 定义测试类和名称
        test_classes = [
            (SinaFinanceAutomationTest, "基础功能测试"),
            (UserLoginAutomationTest, "用户登录功能测试"),
            (NewsAutomationTest, "新闻功能测试")
        ]
        
        all_results = []
        
        try:
            # 依次运行每个测试类
            for test_class, test_name in test_classes:
                try:
                    result = self.run_test_class(test_class, test_name)
                    all_results.append((test_name, result))
                except Exception as e:
                    print(f"运行 {test_name} 时发生错误: {str(e)}")
                    self.total_results['errors'] += 1
                
                # 测试类之间的间隔
                time.sleep(2)
        
        except KeyboardInterrupt:
            print("\n测试被用户中断")
        
        except Exception as e:
            print(f"\n测试执行过程中发生未预期错误: {str(e)}")
        
        finally:
            self.end_time = datetime.now()
            self.print_footer()
        
        return all_results
    
    def generate_report(self, results):
        """生成测试报告"""
        report_filename = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write("新浪财经网站自动化测试报告\n")
                f.write("=" * 50 + "\n")
                f.write(f"测试时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')} - {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"测试目标: https://finance.sina.com.cn/\n\n")
                
                f.write("测试结果汇总:\n")
                f.write(f"总测试用例数: {self.total_results['tests_run']}\n")
                f.write(f"成功: {self.total_results['success']}\n")
                f.write(f"失败: {self.total_results['failures']}\n")
                f.write(f"错误: {self.total_results['errors']}\n")
                
                success_rate = (self.total_results['success'] / self.total_results['tests_run'] * 100) if self.total_results['tests_run'] > 0 else 0
                f.write(f"成功率: {success_rate:.1f}%\n\n")
                
                f.write("详细测试结果:\n")
                f.write("-" * 30 + "\n")
                
                for test_name, result in results:
                    f.write(f"\n{test_name}:\n")
                    f.write(f"  测试数: {result.testsRun}\n")
                    f.write(f"  成功: {result.testsRun - len(result.failures) - len(result.errors)}\n")
                    f.write(f"  失败: {len(result.failures)}\n")
                    f.write(f"  错误: {len(result.errors)}\n")
                    
                    if result.failures:
                        f.write("  失败用例:\n")
                        for test, traceback in result.failures:
                            f.write(f"    - {test}\n")
                    
                    if result.errors:
                        f.write("  错误用例:\n")
                        for test, traceback in result.errors:
                            f.write(f"    - {test}\n")
            
            print(f"\n测试报告已生成: {report_filename}")
            
        except Exception as e:
            print(f"生成测试报告时发生错误: {str(e)}")


def check_dependencies():
    """检查依赖项"""
    try:
        from selenium import webdriver
        print("✓ Selenium 已安装")
    except ImportError:
        print("✗ 请安装 Selenium: pip install selenium")
        return False
    
    try:
        # 检查 ChromeDriver
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.quit()
        print("✓ ChromeDriver 可用")
        return True
    except Exception as e:
        print(f"✗ ChromeDriver 不可用: {str(e)}")
        print("请确保已安装 ChromeDriver 并添加到 PATH")
        return False


def main():
    """主函数"""
    print("检查测试环境...")
    
    if not check_dependencies():
        print("环境检查失败，请安装必要的依赖项")
        return 1
    
    print("环境检查通过，开始执行测试...\n")
    
    # 创建测试运行器
    runner = TestSuiteRunner()
    
    # 运行所有测试
    results = runner.run_all_tests()
    
    # 生成测试报告
    runner.generate_report(results)
    
    # 根据测试结果返回退出码
    if runner.total_results['failures'] > 0 or runner.total_results['errors'] > 0:
        return 1
    else:
        return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
