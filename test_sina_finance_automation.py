#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新浪财经网站自动化测试用例
测试目标：https://finance.sina.com.cn/
"""

import unittest
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class SinaFinanceAutomationTest(unittest.TestCase):
    """新浪财经网站自动化测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化设置"""
        # Chrome浏览器配置
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        # 如果需要无头模式，取消下面注释
        # chrome_options.add_argument('--headless')
        
        cls.driver = webdriver.Chrome(options=chrome_options)
        cls.driver.implicitly_wait(10)
        cls.wait = WebDriverWait(cls.driver, 15)
        cls.base_url = "https://finance.sina.com.cn/"
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        if cls.driver:
            cls.driver.quit()
    
    def setUp(self):
        """每个测试用例前的设置"""
        self.driver.get(self.base_url)
        time.sleep(2)  # 等待页面加载
    
    def test_01_homepage_loading(self):
        """
        测试用例1：首页加载测试
        对应需求：R001_行情数据_实时显示
        测试步骤：
        1. 访问新浪财经首页
        2. 验证页面标题
        3. 验证关键元素是否存在
        4. 验证页面加载时间
        """
        print("\n=== 执行测试用例1：首页加载测试 ===")
        
        start_time = time.time()
        
        try:
            # 验证页面标题
            expected_title_keywords = ["新浪财经", "财经", "股票"]
            actual_title = self.driver.title
            print(f"页面标题: {actual_title}")
            
            title_contains_keyword = any(keyword in actual_title for keyword in expected_title_keywords)
            self.assertTrue(title_contains_keyword, f"页面标题应包含财经相关关键词，实际标题：{actual_title}")
            
            # 验证关键元素存在
            key_elements = [
                (By.CLASS_NAME, "nav"),  # 导航栏
                (By.TAG_NAME, "header"),  # 页头
                (By.TAG_NAME, "main"),   # 主要内容区
            ]
            
            for locator_type, locator_value in key_elements:
                try:
                    element = self.wait.until(
                        EC.presence_of_element_located((locator_type, locator_value))
                    )
                    self.assertTrue(element.is_displayed(), f"关键元素 {locator_value} 应该可见")
                    print(f"✓ 关键元素 {locator_value} 加载成功")
                except TimeoutException:
                    print(f"✗ 关键元素 {locator_value} 加载失败")
            
            # 验证页面加载时间
            load_time = time.time() - start_time
            print(f"页面加载时间: {load_time:.2f}秒")
            self.assertLess(load_time, 10, f"页面加载时间应小于10秒，实际：{load_time:.2f}秒")
            
            # 验证页面内容不为空
            page_source = self.driver.page_source
            self.assertGreater(len(page_source), 1000, "页面内容应该足够丰富")
            
            print("✓ 首页加载测试通过")
            
        except Exception as e:
            print(f"✗ 首页加载测试失败: {str(e)}")
            self.fail(f"首页加载测试失败: {str(e)}")
    
    def test_02_stock_market_data(self):
        """
        测试用例2：股票行情数据显示测试
        对应需求：R001_行情数据_实时显示
        测试步骤：
        1. 访问股票行情页面
        2. 验证上证指数数据显示
        3. 验证数据格式正确性
        4. 验证数据实时性
        """
        print("\n=== 执行测试用例2：股票行情数据显示测试 ===")
        
        try:
            # 查找并点击股票相关链接
            stock_selectors = [
                "//a[contains(text(), '股票')]",
                "//a[contains(text(), '沪深')]", 
                "//a[contains(text(), '行情')]",
                "//a[contains(@href, 'stock')]"
            ]
            
            stock_link_found = False
            for selector in stock_selectors:
                try:
                    stock_link = self.driver.find_element(By.XPATH, selector)
                    if stock_link.is_displayed():
                        print(f"找到股票链接: {stock_link.text}")
                        stock_link.click()
                        stock_link_found = True
                        break
                except NoSuchElementException:
                    continue
            
            if not stock_link_found:
                print("未找到股票链接，直接访问股票页面")
                self.driver.get("https://finance.sina.com.cn/stock/")
            
            time.sleep(3)  # 等待页面加载
            
            # 验证股票数据元素
            stock_data_selectors = [
                "//span[contains(@class, 'price')]",
                "//span[contains(@class, 'change')]", 
                "//div[contains(@class, 'stock')]",
                "//table[contains(@class, 'list')]",
                "//*[contains(text(), '上证指数') or contains(text(), '深证成指')]"
            ]
            
            data_found = False
            for selector in stock_data_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        print(f"✓ 找到股票数据元素: {len(elements)}个")
                        data_found = True
                        
                        # 验证数据格式
                        for i, element in enumerate(elements[:3]):  # 只检查前3个元素
                            text = element.text.strip()
                            if text:
                                print(f"  数据{i+1}: {text}")
                        break
                except Exception as e:
                    continue
            
            self.assertTrue(data_found, "应该能找到股票行情数据")
            
            # 验证页面包含股票相关内容
            page_content = self.driver.page_source.lower()
            stock_keywords = ['股票', '指数', '涨跌', '成交量', '行情']
            keyword_found = any(keyword in page_content for keyword in stock_keywords)
            self.assertTrue(keyword_found, "页面应包含股票相关关键词")
            
            print("✓ 股票行情数据显示测试通过")
            
        except Exception as e:
            print(f"✗ 股票行情数据显示测试失败: {str(e)}")
            self.fail(f"股票行情数据显示测试失败: {str(e)}")
    
    def test_03_search_functionality(self):
        """
        测试用例3：搜索功能测试
        对应需求：R008_新闻_搜索功能
        测试步骤：
        1. 定位搜索框
        2. 输入搜索关键词"茅台"
        3. 执行搜索
        4. 验证搜索结果
        """
        print("\n=== 执行测试用例3：搜索功能测试 ===")
        
        search_keyword = "茅台"
        
        try:
            # 查找搜索框
            search_selectors = [
                "//input[@type='text' and (@placeholder='搜索' or @placeholder='请输入关键词')]",
                "//input[contains(@class, 'search')]",
                "//input[@name='q']",
                "//input[@id='search']",
                "//form//input[@type='text']"
            ]
            
            search_box = None
            for selector in search_selectors:
                try:
                    search_box = self.driver.find_element(By.XPATH, selector)
                    if search_box.is_displayed():
                        print(f"✓ 找到搜索框: {selector}")
                        break
                except NoSuchElementException:
                    continue
            
            if search_box is None:
                print("未找到搜索框，尝试其他方式")
                # 尝试访问搜索页面
                search_url = "https://search.sina.com.cn/"
                self.driver.get(search_url)
                time.sleep(2)
                
                search_box = self.driver.find_element(By.XPATH, "//input[@type='text']")
            
            self.assertIsNotNone(search_box, "应该能找到搜索框")
            
            # 清空搜索框并输入关键词
            search_box.clear()
            search_box.send_keys(search_keyword)
            print(f"✓ 输入搜索关键词: {search_keyword}")
            
            # 查找并点击搜索按钮
            search_button_selectors = [
                "//button[@type='submit']",
                "//input[@type='submit']",
                "//button[contains(text(), '搜索')]",
                "//a[contains(text(), '搜索')]"
            ]
            
            search_executed = False
            for selector in search_button_selectors:
                try:
                    search_button = self.driver.find_element(By.XPATH, selector)
                    if search_button.is_displayed():
                        search_button.click()
                        search_executed = True
                        print("✓ 点击搜索按钮")
                        break
                except NoSuchElementException:
                    continue
            
            if not search_executed:
                # 尝试按回车键
                from selenium.webdriver.common.keys import Keys
                search_box.send_keys(Keys.RETURN)
                print("✓ 按回车键执行搜索")
            
            # 等待搜索结果加载
            time.sleep(3)
            
            # 验证搜索结果
            current_url = self.driver.current_url
            print(f"当前URL: {current_url}")
            
            # 检查URL是否包含搜索相关参数
            search_url_indicators = ['search', 'query', 'q=', 'keyword']
            url_contains_search = any(indicator in current_url.lower() for indicator in search_url_indicators)
            
            # 检查页面内容是否包含搜索关键词
            page_content = self.driver.page_source
            keyword_in_content = search_keyword in page_content
            
            # 查找搜索结果元素
            result_selectors = [
                "//div[contains(@class, 'result')]",
                "//div[contains(@class, 'search')]",
                "//li[contains(@class, 'result')]",
                f"//*[contains(text(), '{search_keyword}')]"
            ]
            
            results_found = False
            for selector in result_selectors:
                try:
                    results = self.driver.find_elements(By.XPATH, selector)
                    if results:
                        print(f"✓ 找到搜索结果: {len(results)}个")
                        results_found = True
                        break
                except Exception:
                    continue
            
            # 验证搜索功能执行成功
            search_success = url_contains_search or keyword_in_content or results_found
            self.assertTrue(search_success, f"搜索功能应该正常工作，关键词：{search_keyword}")
            
            if keyword_in_content:
                print(f"✓ 页面内容包含搜索关键词: {search_keyword}")
            
            print("✓ 搜索功能测试通过")
            
        except Exception as e:
            print(f"✗ 搜索功能测试失败: {str(e)}")
            self.fail(f"搜索功能测试失败: {str(e)}")


def run_tests():
    """运行所有测试用例"""
    print("=" * 60)
    print("新浪财经网站自动化测试开始")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(SinaFinanceAutomationTest)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print("=" * 60)
    
    return result


if __name__ == "__main__":
    # 运行测试
    test_result = run_tests()
    
    # 根据测试结果设置退出码
    if test_result.failures or test_result.errors:
        exit(1)
    else:
        exit(0)
