测试原始需求编号,测试原始需求描述,优先级,所属功能模块,需求来源,需求描述（给自己出题）,软件需求ID,软件需求描述,测试需求ID,测试需求描述,测试子项,测试用例编号,测试用例标题,测试步骤,预期结果,测试方法,测试类型,执行状态,实际结果,缺陷ID
新浪财经-001,系统应实时显示股票、基金、期货等金融产品行情数据,高,行情数据,软件需求说明书,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据数据延迟不超过15分钟,R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,股票行情显示,TC001,股票行情正常显示,"1.打开新浪财经首页;2.点击股票菜单;3.查看上证指数行情","显示当前价格、涨跌幅、成交量等信息",等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,基金行情显示,TC002,基金行情正常显示,"1.访问基金页面
2.选择任意基金
3.查看基金净值","显示最新净值、涨跌幅、基金规模等信息",高,等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,期货行情显示,TC003,期货行情正常显示,"1.访问期货页面
2.选择主力合约
3.查看期货价格","显示最新价格、涨跌幅、持仓量等信息",高,等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,外汇行情显示,TC004,外汇行情正常显示,"1.访问外汇页面
2.选择USD/CNY
3.查看汇率信息","显示实时汇率、涨跌幅、买卖价等信息",高,等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,黄金行情显示,TC005,黄金行情正常显示,"1.访问黄金页面
2.查看现货黄金价格
3.验证数据完整性","显示黄金价格、涨跌幅、成交量等信息",高,等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,数据自动刷新,TC006,行情数据自动刷新,"1.打开任意行情页面
2.等待15秒
3.观察数据是否更新",数据在15秒内自动刷新,高,边界值分析,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,历史数据查询,TC007,历史数据查询,"1.选择任意股票
2.点击""历史数据""
3.选择查询时间范围",显示指定时间范围的历史行情数据,中,等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,K线图显示,TC008,K线图正常显示,"1.进入股票详情页
2.查看K线图
3.切换不同时间周期","K线图正常显示，支持日K、周K、月K切换",中,等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,分时图显示,TC009,分时图正常显示,"1.进入股票详情页
2.点击分时图
3.查看实时走势","分时图实时更新，显示当日价格走势",中,等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,成交量显示,TC010,成交量显示,"1.查看任意股票
2.观察成交量数据
3.验证数据格式",成交量以万手或亿元为单位正确显示,中,等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,网络异常处理,TC011,网络断开时行情显示,"1.打开行情页面
2.断开网络连接
3.观察页面表现","显示网络错误提示，数据停止更新",高,边界值分析,异常测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,无效代码处理,TC012,无效股票代码查询,"1.在搜索框输入""999999""
2.点击搜索
3.查看结果","提示""未找到相关股票""或类似错误信息",中,边界值分析,异常测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,特殊字符处理,TC013,特殊字符输入测试,"1.在搜索框输入""@#$%""
2.点击搜索
3.查看系统响应","系统正常处理，不出现错误页面",中,边界值分析,异常测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,长度限制测试,TC014,超长字符串输入,"1.输入超过100个字符的股票代码
2.提交查询
3.观察系统行为",系统限制输入长度或给出合理提示,低,边界值分析,异常测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,超时处理,TC015,服务器响应超时,"1.模拟服务器响应延迟
2.访问行情页面
3.观察页面加载",显示加载提示或超时错误信息,中,边界值分析,性能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,新闻浏览,TC016,新闻正常浏览,"1.访问新浪财经首页
2.点击任意新闻标题
3.阅读新闻内容","新闻内容完整显示，包含标题、正文、时间、来源",高,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,新闻分类,TC017,新闻分类浏览,"1.点击""股票""分类
2.查看股票相关新闻
3.验证新闻相关性","显示股票相关新闻，分类准确",高,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,新闻搜索,TC018,新闻搜索功能,"1.在搜索框输入""茅台""
2.点击搜索
3.查看搜索结果","返回包含""茅台""关键词的相关新闻",中,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,时间排序,TC019,新闻时间排序,"1.进入新闻列表页
2.查看新闻发布时间
3.验证排序规则",新闻按发布时间倒序排列,中,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,分页功能,TC020,新闻分页功能,"1.浏览新闻列表
2.点击""下一页""
3.查看更多新闻","分页功能正常，可以查看更多历史新闻",中,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,分享功能,TC021,新闻分享功能,"1.打开任意新闻
2.点击分享按钮
3.选择分享平台","可以分享到微博、微信等社交平台",低,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,相关推荐,TC022,相关新闻推荐,"1.阅读某条新闻
2.查看页面底部
3.验证相关新闻",显示相关主题的新闻推荐,低,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,图片显示,TC023,新闻图片显示,"1.打开包含图片的新闻
2.查看图片加载情况
3.点击图片放大","图片正常加载显示，支持放大查看",中,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,评论查看,TC024,新闻评论查看,"1.打开新闻详情页
2.滚动到评论区
3.查看用户评论","显示用户评论，包含用户名、评论内容、时间",低,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,热门排行,TC025,热门新闻排行,"1.查看首页热门新闻
2.验证排行依据
3.点击查看详情",热门新闻按阅读量或热度排序,中,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,空搜索处理,TC026,搜索空关键词,"1.在搜索框不输入任何内容
2.点击搜索
3.查看系统响应","提示""请输入搜索关键词""或类似提示",中,边界值分析,异常测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,特殊字符搜索,TC027,搜索特殊字符,"1.输入""<script>alert(1)</script>""
2.点击搜索
3.观察页面反应","系统正常处理，不执行脚本代码",高,边界值分析,安全测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,无效新闻访问,TC028,访问不存在的新闻,"1.修改URL中的新闻ID为无效值
2.访问该URL
3.查看页面显示","显示404错误页面或""新闻不存在""提示",中,边界值分析,异常测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,图片加载失败,TC029,新闻图片加载失败,"1.访问包含图片的新闻
2.模拟图片加载失败
3.查看页面表现",显示图片加载失败的占位符或提示,低,边界值分析,异常测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,评论异常,TC030,评论功能异常,"1.尝试发表空评论
2.提交评论
3.观察系统响应","提示""评论内容不能为空""或类似错误信息",低,边界值分析,异常测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,手机号注册,TC031,手机号注册成功,"1.点击""注册""按钮
2.输入有效手机号
3.输入验证码和密码
4.提交注册","注册成功，跳转到登录页面或自动登录",高,等价类划分,功能测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,邮箱注册,TC032,邮箱注册成功,"1.选择邮箱注册
2.输入有效邮箱地址
3.设置密码
4.验证邮箱","注册成功，收到验证邮件",高,等价类划分,功能测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,用户名登录,TC033,用户名密码登录,"1.输入已注册的用户名
2.输入正确密码
3.点击登录","登录成功，跳转到用户中心或首页",高,等价类划分,功能测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,手机号登录,TC034,手机号密码登录,"1.输入已注册手机号
2.输入正确密码
3.点击登录","登录成功，显示用户信息",高,等价类划分,功能测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,第三方登录,TC035,第三方登录(微博),"1.点击""微博登录""
2.授权登录
3.完成绑定",通过微博账号成功登录系统,中,等价类划分,功能测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,记住登录,TC036,记住登录状态,"1.登录时勾选""记住我""
2.关闭浏览器
3.重新打开网站","自动保持登录状态，无需重新登录",中,等价类划分,功能测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,修改信息,TC037,修改个人信息,"1.登录后进入个人中心
2.修改昵称和头像
3.保存修改","个人信息修改成功，页面显示更新",中,等价类划分,功能测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,修改密码,TC038,修改登录密码,"1.进入账户设置
2.输入原密码
3.设置新密码
4.确认修改","密码修改成功，下次需用新密码登录",中,等价类划分,功能测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,找回密码,TC039,找回密码功能,"1.点击""忘记密码""
2.输入手机号
3.获取验证码
4.重置密码","密码重置成功，可用新密码登录",中,等价类划分,功能测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,安全退出,TC040,安全退出登录,"1.点击用户头像
2.选择""退出登录""
3.确认退出","成功退出登录，清除登录状态",中,等价类划分,功能测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,无效手机号,TC041,无效手机号注册,"1.输入""123456789""
2.尝试注册
3.查看提示信息","提示""请输入正确的手机号码""",高,边界值分析,异常测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,重复注册,TC042,重复手机号注册,"1.使用已注册手机号
2.尝试再次注册
3.观察系统响应","提示""该手机号已被注册""",高,边界值分析,异常测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,弱密码,TC043,弱密码设置,"1.设置密码为""123""
2.提交注册
3.查看验证结果","提示""密码强度太弱，请设置复杂密码""",中,边界值分析,异常测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,错误密码,TC044,错误密码登录,"1.输入正确用户名
2.输入错误密码
3.尝试登录","提示""用户名或密码错误""",高,边界值分析,异常测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,不存在用户,TC045,不存在用户登录,"1.输入不存在的用户名
2.输入任意密码
3.尝试登录","提示""用户名或密码错误""",高,边界值分析,异常测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,SQL注入防护,TC046,SQL注入攻击测试,"1.在用户名输入""admin' OR '1'='1""
2.尝试登录
3.观察系统行为","系统正常处理，不发生SQL注入",高,边界值分析,安全测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,暴力破解防护,TC047,密码暴力破解防护,"1.连续输入错误密码10次
2.观察系统响应
3.验证防护机制",账户被临时锁定或要求验证码,高,边界值分析,安全测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,会话超时,TC048,会话超时测试,"1.登录系统
2.长时间不操作(30分钟)
3.尝试访问需要登录的页面","会话超时，要求重新登录",中,边界值分析,安全测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,XSS防护,TC049,跨站脚本攻击防护,"1.在个人信息中输入""<script>alert('XSS')</script>""
2.保存信息
3.查看页面显示","脚本被过滤，不执行恶意代码",高,边界值分析,安全测试,未执行,,
R011,用户可以注册账号并登录系统,R011_用户_注册登录,用户可通过手机号或邮箱注册账号支持密码登录和第三方登录,邮箱格式验证,TC050,无效邮箱格式,"1.输入""invalid-email""
2.尝试注册
3.查看验证结果","提示""请输入正确的邮箱格式""",中,边界值分析,异常测试,未执行,,
