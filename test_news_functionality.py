#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新浪财经新闻功能自动化测试
测试目标：新闻内容发布和浏览功能
对应需求：R006_新闻_内容发布, R008_新闻_搜索功能
"""

import unittest
import time
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains


class NewsAutomationTest(unittest.TestCase):
    """新闻功能自动化测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化设置"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        cls.driver = webdriver.Chrome(options=chrome_options)
        cls.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        cls.driver.implicitly_wait(10)
        cls.wait = WebDriverWait(cls.driver, 15)
        cls.base_url = "https://finance.sina.com.cn/"
        
        # 测试关键词
        cls.search_keywords = ['茅台', '股票', '基金', '经济']
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        if cls.driver:
            cls.driver.quit()
    
    def setUp(self):
        """每个测试用例前的设置"""
        self.driver.get(self.base_url)
        time.sleep(2)
    
    def find_news_elements(self):
        """查找新闻相关元素"""
        news_selectors = [
            "//a[contains(@href, 'news') or contains(@href, 'article')]",
            "//div[contains(@class, 'news')]//a",
            "//h1//a | //h2//a | //h3//a",
            "//a[contains(text(), '新闻') or contains(text(), '资讯')]",
            "//*[@class='title']//a | //*[@class='headline']//a"
        ]
        
        news_links = []
        for selector in news_selectors:
            try:
                elements = self.driver.find_elements(By.XPATH, selector)
                for element in elements:
                    if element.is_displayed() and element.get_attribute('href'):
                        href = element.get_attribute('href')
                        text = element.text.strip()
                        if text and len(text) > 5:  # 过滤掉太短的标题
                            news_links.append((element, text, href))
                if news_links:
                    break
            except Exception:
                continue
        
        return news_links[:10]  # 返回前10个新闻链接
    
    def test_01_news_list_display(self):
        """
        测试用例1：新闻列表显示测试
        对应需求：R006_新闻_内容发布
        测试步骤：
        1. 访问新浪财经首页
        2. 查找新闻列表
        3. 验证新闻标题和链接
        4. 验证新闻时间信息
        """
        print("\n=== 执行测试用例1：新闻列表显示测试 ===")
        
        try:
            # 查找新闻元素
            news_links = self.find_news_elements()
            
            self.assertGreater(len(news_links), 0, "应该能找到新闻列表")
            print(f"✓ 找到 {len(news_links)} 条新闻")
            
            # 验证新闻标题和链接
            valid_news_count = 0
            for i, (element, title, href) in enumerate(news_links[:5]):  # 检查前5条新闻
                print(f"新闻 {i+1}: {title[:50]}...")
                
                # 验证标题不为空
                self.assertGreater(len(title), 0, f"新闻标题不应为空: {title}")
                
                # 验证链接有效
                self.assertTrue(href.startswith('http'), f"新闻链接应该是有效URL: {href}")
                
                # 验证标题包含中文字符（财经新闻通常包含中文）
                chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
                has_chinese = bool(chinese_pattern.search(title))
                if has_chinese:
                    valid_news_count += 1
            
            self.assertGreater(valid_news_count, 0, "应该有包含中文的新闻标题")
            
            # 查找时间信息
            time_selectors = [
                "//*[contains(@class, 'time') or contains(@class, 'date')]",
                "//*[contains(text(), '年') and contains(text(), '月')]",
                "//*[contains(text(), ':') and (contains(text(), '20') or contains(text(), '今天'))]"
            ]
            
            time_found = False
            for selector in time_selectors:
                try:
                    time_elements = self.driver.find_elements(By.XPATH, selector)
                    for element in time_elements:
                        if element.is_displayed() and element.text.strip():
                            print(f"✓ 找到时间信息: {element.text}")
                            time_found = True
                            break
                    if time_found:
                        break
                except Exception:
                    continue
            
            if not time_found:
                print("⚠ 未找到明确的时间信息，但新闻列表显示正常")
            
            print("✓ 新闻列表显示测试通过")
            
        except Exception as e:
            print(f"✗ 新闻列表显示测试失败: {str(e)}")
            self.fail(f"新闻列表显示测试失败: {str(e)}")
    
    def test_02_news_content_access(self):
        """
        测试用例2：新闻内容访问测试
        对应需求：R006_新闻_内容发布
        测试步骤：
        1. 从首页选择一条新闻
        2. 点击新闻链接
        3. 验证新闻详情页面
        4. 验证新闻内容完整性
        """
        print("\n=== 执行测试用例2：新闻内容访问测试 ===")
        
        try:
            # 查找新闻链接
            news_links = self.find_news_elements()
            
            if not news_links:
                self.skipTest("未找到新闻链接")
            
            # 选择第一条新闻
            first_news_element, first_news_title, first_news_href = news_links[0]
            print(f"选择新闻: {first_news_title[:50]}...")
            
            # 记录原始窗口
            original_window = self.driver.current_window_handle
            
            # 点击新闻链接
            try:
                # 尝试在新标签页打开
                ActionChains(self.driver).key_down(Keys.CONTROL).click(first_news_element).key_up(Keys.CONTROL).perform()
                time.sleep(2)
                
                # 切换到新标签页
                windows = self.driver.window_handles
                if len(windows) > 1:
                    self.driver.switch_to.window(windows[-1])
                else:
                    # 如果没有新标签页，直接点击
                    first_news_element.click()
                    
            except Exception:
                # 如果上述方法失败，直接访问URL
                self.driver.get(first_news_href)
            
            time.sleep(3)
            
            # 验证新闻详情页面
            current_url = self.driver.current_url
            print(f"当前页面URL: {current_url}")
            
            # 验证URL变化
            self.assertNotEqual(current_url, self.base_url, "应该跳转到新闻详情页面")
            
            # 查找新闻内容元素
            content_selectors = [
                "//div[contains(@class, 'content') or contains(@class, 'article')]",
                "//div[contains(@class, 'text') or contains(@class, 'body')]",
                "//p[string-length(text()) > 20]",  # 查找包含较长文本的段落
                "//*[contains(@class, 'news-content')]"
            ]
            
            content_found = False
            content_text = ""
            for selector in content_selectors:
                try:
                    content_elements = self.driver.find_elements(By.XPATH, selector)
                    for element in content_elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            if len(text) > 50:  # 内容应该足够长
                                content_text = text
                                content_found = True
                                print(f"✓ 找到新闻内容，长度: {len(text)} 字符")
                                break
                    if content_found:
                        break
                except Exception:
                    continue
            
            # 验证页面标题
            page_title = self.driver.title
            print(f"页面标题: {page_title}")
            self.assertGreater(len(page_title), 0, "页面应该有标题")
            
            # 验证新闻内容
            if content_found:
                # 验证内容长度
                self.assertGreater(len(content_text), 50, "新闻内容应该足够详细")
                
                # 验证内容包含中文
                chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
                has_chinese = bool(chinese_pattern.search(content_text))
                self.assertTrue(has_chinese, "新闻内容应该包含中文")
                
                print("✓ 新闻内容验证通过")
            else:
                print("⚠ 未找到明确的新闻内容区域，但页面加载正常")
            
            # 查找新闻元信息（来源、时间等）
            meta_selectors = [
                "//*[contains(text(), '来源') or contains(text(), '作者')]",
                "//*[contains(@class, 'source') or contains(@class, 'author')]",
                "//*[contains(@class, 'meta') or contains(@class, 'info')]"
            ]
            
            meta_found = False
            for selector in meta_selectors:
                try:
                    meta_elements = self.driver.find_elements(By.XPATH, selector)
                    for element in meta_elements:
                        if element.is_displayed() and element.text.strip():
                            print(f"✓ 找到元信息: {element.text}")
                            meta_found = True
                            break
                    if meta_found:
                        break
                except Exception:
                    continue
            
            print("✓ 新闻内容访问测试通过")
            
            # 关闭新标签页并返回原窗口
            if len(self.driver.window_handles) > 1:
                self.driver.close()
                self.driver.switch_to.window(original_window)
            
        except Exception as e:
            print(f"✗ 新闻内容访问测试失败: {str(e)}")
            # 确保返回原窗口
            try:
                if len(self.driver.window_handles) > 1:
                    self.driver.close()
                    self.driver.switch_to.window(self.driver.window_handles[0])
            except:
                pass
            self.fail(f"新闻内容访问测试失败: {str(e)}")
    
    def test_03_news_search_function(self):
        """
        测试用例3：新闻搜索功能测试
        对应需求：R008_新闻_搜索功能
        测试步骤：
        1. 查找搜索框
        2. 输入搜索关键词
        3. 执行搜索
        4. 验证搜索结果相关性
        """
        print("\n=== 执行测试用例3：新闻搜索功能测试 ===")
        
        search_keyword = self.search_keywords[0]  # 使用"茅台"作为搜索关键词
        
        try:
            # 查找搜索框
            search_selectors = [
                "//input[@type='text' and (@placeholder='搜索' or @placeholder='请输入关键词' or @name='q')]",
                "//input[contains(@class, 'search')]",
                "//form//input[@type='text']",
                "//input[@id='search' or @id='searchInput']"
            ]
            
            search_box = None
            for selector in search_selectors:
                try:
                    search_box = self.driver.find_element(By.XPATH, selector)
                    if search_box.is_displayed():
                        print(f"✓ 找到搜索框: {selector}")
                        break
                except NoSuchElementException:
                    continue
            
            if search_box is None:
                print("首页未找到搜索框，尝试访问搜索页面")
                search_urls = [
                    "https://search.sina.com.cn/",
                    "https://finance.sina.com.cn/search/",
                    "https://search.finance.sina.com.cn/"
                ]
                
                for url in search_urls:
                    try:
                        self.driver.get(url)
                        time.sleep(2)
                        search_box = self.driver.find_element(By.XPATH, "//input[@type='text']")
                        if search_box.is_displayed():
                            print(f"✓ 在搜索页面找到搜索框: {url}")
                            break
                    except Exception:
                        continue
            
            self.assertIsNotNone(search_box, "应该能找到搜索框")
            
            # 输入搜索关键词
            search_box.clear()
            search_box.send_keys(search_keyword)
            print(f"✓ 输入搜索关键词: {search_keyword}")
            
            # 执行搜索
            search_executed = False
            
            # 方法1：查找搜索按钮
            search_button_selectors = [
                "//button[@type='submit' or contains(text(), '搜索')]",
                "//input[@type='submit']",
                "//a[contains(text(), '搜索')]"
            ]
            
            for selector in search_button_selectors:
                try:
                    search_button = self.driver.find_element(By.XPATH, selector)
                    if search_button.is_displayed():
                        search_button.click()
                        search_executed = True
                        print("✓ 点击搜索按钮")
                        break
                except NoSuchElementException:
                    continue
            
            # 方法2：按回车键
            if not search_executed:
                search_box.send_keys(Keys.RETURN)
                search_executed = True
                print("✓ 按回车键执行搜索")
            
            # 等待搜索结果
            time.sleep(3)
            
            # 验证搜索结果
            current_url = self.driver.current_url
            print(f"搜索后URL: {current_url}")
            
            # 检查URL是否包含搜索参数
            search_url_indicators = ['search', 'query', 'q=', 'keyword', search_keyword]
            url_has_search_params = any(indicator in current_url.lower() for indicator in search_url_indicators)
            
            # 查找搜索结果
            result_selectors = [
                "//div[contains(@class, 'result') or contains(@class, 'search')]",
                "//li[contains(@class, 'result')]",
                f"//a[contains(text(), '{search_keyword}')]",
                f"//*[contains(text(), '{search_keyword}')]",
                "//h3//a | //h2//a"  # 搜索结果标题
            ]
            
            search_results = []
            for selector in result_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            if text and len(text) > 5:
                                search_results.append(text)
                    if search_results:
                        break
                except Exception:
                    continue
            
            # 验证搜索功能
            if search_results:
                print(f"✓ 找到 {len(search_results)} 个搜索结果")
                
                # 验证结果相关性
                relevant_results = 0
                for result in search_results[:5]:  # 检查前5个结果
                    if search_keyword in result:
                        relevant_results += 1
                        print(f"  相关结果: {result[:50]}...")
                
                if relevant_results > 0:
                    print(f"✓ 找到 {relevant_results} 个相关结果")
                else:
                    print("⚠ 搜索结果可能不够相关，但搜索功能正常工作")
                
            elif url_has_search_params:
                print("✓ URL包含搜索参数，搜索功能正常执行")
            else:
                # 检查页面内容是否包含搜索关键词
                page_content = self.driver.page_source
                if search_keyword in page_content:
                    print("✓ 页面内容包含搜索关键词")
                else:
                    print("⚠ 搜索结果不明确，但搜索功能已执行")
            
            # 验证搜索功能正常工作
            search_works = bool(search_results) or url_has_search_params or (search_keyword in self.driver.page_source)
            self.assertTrue(search_works, f"搜索功能应该正常工作，关键词：{search_keyword}")
            
            print("✓ 新闻搜索功能测试通过")
            
        except Exception as e:
            print(f"✗ 新闻搜索功能测试失败: {str(e)}")
            self.fail(f"新闻搜索功能测试失败: {str(e)}")


def run_news_tests():
    """运行新闻功能测试用例"""
    print("=" * 60)
    print("新浪财经新闻功能自动化测试开始")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(NewsAutomationTest)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("\n" + "=" * 60)
    print("新闻功能测试结果汇总:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print("=" * 60)
    
    return result


if __name__ == "__main__":
    # 运行测试
    test_result = run_news_tests()
    
    # 根据测试结果设置退出码
    if test_result.failures or test_result.errors:
        exit(1)
    else:
        exit(0)
