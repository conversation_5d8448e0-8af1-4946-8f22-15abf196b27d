#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Selenium测试 - 诊断环境问题
"""

import sys
print(f"Python版本: {sys.version}")

try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    print("✓ Selenium导入成功")
except ImportError as e:
    print(f"✗ Selenium导入失败: {e}")
    sys.exit(1)

try:
    print("正在配置Chrome选项...")
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1280,720')
    chrome_options.add_argument('--disable-extensions')
    chrome_options.add_argument('--disable-plugins')
    chrome_options.add_argument('--disable-images')
    print("✓ Chrome选项配置完成")
    
    print("正在启动Chrome浏览器...")
    driver = webdriver.Chrome(options=chrome_options)
    print("✓ Chrome浏览器启动成功")
    
    print("正在访问百度...")
    driver.get("https://www.baidu.com")
    print(f"✓ 页面标题: {driver.title}")
    
    print("正在访问新浪财经...")
    driver.get("https://finance.sina.com.cn/")
    print(f"✓ 页面标题: {driver.title}")
    
    driver.quit()
    print("✓ 浏览器已关闭")
    print("\n🎉 环境测试成功！")
    
except Exception as e:
    print(f"✗ 测试失败: {e}")
    print(f"错误类型: {type(e).__name__}")
    
    # 尝试使用webdriver-manager自动下载ChromeDriver
    try:
        print("\n尝试使用webdriver-manager自动配置...")
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        print("✓ 使用webdriver-manager启动成功")
        
        driver.get("https://www.baidu.com")
        print(f"✓ 页面标题: {driver.title}")
        
        driver.quit()
        print("✓ 测试成功")
        
    except Exception as e2:
        print(f"✗ webdriver-manager也失败: {e2}")
        print("\n可能的解决方案:")
        print("1. 确保Chrome浏览器已安装")
        print("2. 下载对应版本的ChromeDriver")
        print("3. 将ChromeDriver添加到PATH环境变量")
        print("4. 或者使用webdriver-manager自动管理")
        sys.exit(1)
