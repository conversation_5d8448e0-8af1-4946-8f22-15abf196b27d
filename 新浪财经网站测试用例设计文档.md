# 新浪财经网站测试用例设计文档

## 1. 文档概述

### 1.1 文档目的
本文档针对新浪财经网站的核心功能设计详细的测试用例，覆盖功能测试、性能测试、兼容性测试等多个方面，确保系统质量。

### 1.2 测试范围
- 覆盖需求: R001_行情数据_实时显示、R006_新闻_内容发布、R011_用户_注册登录等3条核心需求
- 测试方法: 等价类划分法、边界值分析法
- 测试用例数量: 50+条

## 2. 测试用例设计

### 2.1 行情数据实时显示功能测试用例 (覆盖需求R001)

#### 2.1.1 正向测试用例

| 用例编号 | 用例标题 | 测试步骤 | 预期结果 | 优先级 | 测试方法 |
|---|---|---|---|---|---|
| TC001 | 股票行情正常显示 | 1.打开新浪财经首页<br>2.点击"股票"菜单<br>3.查看上证指数行情 | 显示当前价格、涨跌幅、成交量等信息 | 高 | 等价类划分 |
| TC002 | 基金行情正常显示 | 1.访问基金页面<br>2.选择任意基金<br>3.查看基金净值 | 显示最新净值、涨跌幅、基金规模等信息 | 高 | 等价类划分 |
| TC003 | 期货行情正常显示 | 1.访问期货页面<br>2.选择主力合约<br>3.查看期货价格 | 显示最新价格、涨跌幅、持仓量等信息 | 高 | 等价类划分 |
| TC004 | 外汇行情正常显示 | 1.访问外汇页面<br>2.选择USD/CNY<br>3.查看汇率信息 | 显示实时汇率、涨跌幅、买卖价等信息 | 高 | 等价类划分 |
| TC005 | 黄金行情正常显示 | 1.访问黄金页面<br>2.查看现货黄金价格<br>3.验证数据完整性 | 显示黄金价格、涨跌幅、成交量等信息 | 高 | 等价类划分 |
| TC006 | 行情数据自动刷新 | 1.打开任意行情页面<br>2.等待15秒<br>3.观察数据是否更新 | 数据在15秒内自动刷新 | 高 | 边界值分析 |
| TC007 | 历史数据查询 | 1.选择任意股票<br>2.点击"历史数据"<br>3.选择查询时间范围 | 显示指定时间范围的历史行情数据 | 中 | 等价类划分 |
| TC008 | K线图正常显示 | 1.进入股票详情页<br>2.查看K线图<br>3.切换不同时间周期 | K线图正常显示，支持日K、周K、月K切换 | 中 | 等价类划分 |
| TC009 | 分时图正常显示 | 1.进入股票详情页<br>2.点击分时图<br>3.查看实时走势 | 分时图实时更新，显示当日价格走势 | 中 | 等价类划分 |
| TC010 | 成交量显示 | 1.查看任意股票<br>2.观察成交量数据<br>3.验证数据格式 | 成交量以万手或亿元为单位正确显示 | 中 | 等价类划分 |

#### 2.1.2 反向测试用例

| 用例编号 | 用例标题 | 测试步骤 | 预期结果 | 优先级 | 测试方法 |
|---|---|---|---|---|---|
| TC011 | 网络断开时行情显示 | 1.打开行情页面<br>2.断开网络连接<br>3.观察页面表现 | 显示网络错误提示，数据停止更新 | 高 | 边界值分析 |
| TC012 | 无效股票代码查询 | 1.在搜索框输入"999999"<br>2.点击搜索<br>3.查看结果 | 提示"未找到相关股票"或类似错误信息 | 中 | 边界值分析 |
| TC013 | 特殊字符输入测试 | 1.在搜索框输入"@#$%"<br>2.点击搜索<br>3.查看系统响应 | 系统正常处理，不出现错误页面 | 中 | 边界值分析 |
| TC014 | 超长字符串输入 | 1.输入超过100个字符的股票代码<br>2.提交查询<br>3.观察系统行为 | 系统限制输入长度或给出合理提示 | 低 | 边界值分析 |
| TC015 | 服务器响应超时 | 1.模拟服务器响应延迟<br>2.访问行情页面<br>3.观察页面加载 | 显示加载提示或超时错误信息 | 中 | 边界值分析 |

### 2.2 新闻内容发布功能测试用例 (覆盖需求R006)

#### 2.2.1 正向测试用例

| 用例编号 | 用例标题 | 测试步骤 | 预期结果 | 优先级 | 测试方法 |
|---|---|---|---|---|---|
| TC016 | 新闻正常浏览 | 1.访问新浪财经首页<br>2.点击任意新闻标题<br>3.阅读新闻内容 | 新闻内容完整显示，包含标题、正文、时间、来源 | 高 | 等价类划分 |
| TC017 | 新闻分类浏览 | 1.点击"股票"分类<br>2.查看股票相关新闻<br>3.验证新闻相关性 | 显示股票相关新闻，分类准确 | 高 | 等价类划分 |
| TC018 | 新闻搜索功能 | 1.在搜索框输入"茅台"<br>2.点击搜索<br>3.查看搜索结果 | 返回包含"茅台"关键词的相关新闻 | 中 | 等价类划分 |
| TC019 | 新闻时间排序 | 1.进入新闻列表页<br>2.查看新闻发布时间<br>3.验证排序规则 | 新闻按发布时间倒序排列 | 中 | 等价类划分 |
| TC020 | 新闻分页功能 | 1.浏览新闻列表<br>2.点击"下一页"<br>3.查看更多新闻 | 分页功能正常，可以查看更多历史新闻 | 中 | 等价类划分 |
| TC021 | 新闻分享功能 | 1.打开任意新闻<br>2.点击分享按钮<br>3.选择分享平台 | 可以分享到微博、微信等社交平台 | 低 | 等价类划分 |
| TC022 | 相关新闻推荐 | 1.阅读某条新闻<br>2.查看页面底部<br>3.验证相关新闻 | 显示相关主题的新闻推荐 | 低 | 等价类划分 |
| TC023 | 新闻图片显示 | 1.打开包含图片的新闻<br>2.查看图片加载情况<br>3.点击图片放大 | 图片正常加载显示，支持放大查看 | 中 | 等价类划分 |
| TC024 | 新闻评论查看 | 1.打开新闻详情页<br>2.滚动到评论区<br>3.查看用户评论 | 显示用户评论，包含用户名、评论内容、时间 | 低 | 等价类划分 |
| TC025 | 热门新闻排行 | 1.查看首页热门新闻<br>2.验证排行依据<br>3.点击查看详情 | 热门新闻按阅读量或热度排序 | 中 | 等价类划分 |

#### 2.2.2 反向测试用例

| 用例编号 | 用例标题 | 测试步骤 | 预期结果 | 优先级 | 测试方法 |
|---|---|---|---|---|---|
| TC026 | 搜索空关键词 | 1.在搜索框不输入任何内容<br>2.点击搜索<br>3.查看系统响应 | 提示"请输入搜索关键词"或类似提示 | 中 | 边界值分析 |
| TC027 | 搜索特殊字符 | 1.输入"<script>alert(1)</script>"<br>2.点击搜索<br>3.观察页面反应 | 系统正常处理，不执行脚本代码 | 高 | 边界值分析 |
| TC028 | 访问不存在的新闻 | 1.修改URL中的新闻ID为无效值<br>2.访问该URL<br>3.查看页面显示 | 显示404错误页面或"新闻不存在"提示 | 中 | 边界值分析 |
| TC029 | 新闻图片加载失败 | 1.访问包含图片的新闻<br>2.模拟图片加载失败<br>3.查看页面表现 | 显示图片加载失败的占位符或提示 | 低 | 边界值分析 |
| TC030 | 评论功能异常 | 1.尝试发表空评论<br>2.提交评论<br>3.观察系统响应 | 提示"评论内容不能为空"或类似错误信息 | 低 | 边界值分析 |

### 2.3 用户注册登录功能测试用例 (覆盖需求R011)

#### 2.3.1 正向测试用例

| 用例编号 | 用例标题 | 测试步骤 | 预期结果 | 优先级 | 测试方法 |
|---|---|---|---|---|---|
| TC031 | 手机号注册成功 | 1.点击"注册"按钮<br>2.输入有效手机号<br>3.输入验证码和密码<br>4.提交注册 | 注册成功，跳转到登录页面或自动登录 | 高 | 等价类划分 |
| TC032 | 邮箱注册成功 | 1.选择邮箱注册<br>2.输入有效邮箱地址<br>3.设置密码<br>4.验证邮箱 | 注册成功，收到验证邮件 | 高 | 等价类划分 |
| TC033 | 用户名密码登录 | 1.输入已注册的用户名<br>2.输入正确密码<br>3.点击登录 | 登录成功，跳转到用户中心或首页 | 高 | 等价类划分 |
| TC034 | 手机号密码登录 | 1.输入已注册手机号<br>2.输入正确密码<br>3.点击登录 | 登录成功，显示用户信息 | 高 | 等价类划分 |
| TC035 | 第三方登录(微博) | 1.点击"微博登录"<br>2.授权登录<br>3.完成绑定 | 通过微博账号成功登录系统 | 中 | 等价类划分 |
| TC036 | 记住登录状态 | 1.登录时勾选"记住我"<br>2.关闭浏览器<br>3.重新打开网站 | 自动保持登录状态，无需重新登录 | 中 | 等价类划分 |
| TC037 | 修改个人信息 | 1.登录后进入个人中心<br>2.修改昵称和头像<br>3.保存修改 | 个人信息修改成功，页面显示更新 | 中 | 等价类划分 |
| TC038 | 修改登录密码 | 1.进入账户设置<br>2.输入原密码<br>3.设置新密码<br>4.确认修改 | 密码修改成功，下次需用新密码登录 | 中 | 等价类划分 |
| TC039 | 找回密码功能 | 1.点击"忘记密码"<br>2.输入手机号<br>3.获取验证码<br>4.重置密码 | 密码重置成功，可用新密码登录 | 中 | 等价类划分 |
| TC040 | 安全退出登录 | 1.点击用户头像<br>2.选择"退出登录"<br>3.确认退出 | 成功退出登录，清除登录状态 | 中 | 等价类划分 |

#### 2.3.2 反向测试用例

| 用例编号 | 用例标题 | 测试步骤 | 预期结果 | 优先级 | 测试方法 |
|---|---|---|---|---|---|
| TC041 | 无效手机号注册 | 1.输入"123456789"<br>2.尝试注册<br>3.查看提示信息 | 提示"请输入正确的手机号码" | 高 | 边界值分析 |
| TC042 | 重复手机号注册 | 1.使用已注册手机号<br>2.尝试再次注册<br>3.观察系统响应 | 提示"该手机号已被注册" | 高 | 边界值分析 |
| TC043 | 弱密码设置 | 1.设置密码为"123"<br>2.提交注册<br>3.查看验证结果 | 提示"密码强度太弱，请设置复杂密码" | 中 | 边界值分析 |
| TC044 | 错误密码登录 | 1.输入正确用户名<br>2.输入错误密码<br>3.尝试登录 | 提示"用户名或密码错误" | 高 | 边界值分析 |
| TC045 | 不存在用户登录 | 1.输入不存在的用户名<br>2.输入任意密码<br>3.尝试登录 | 提示"用户名或密码错误" | 高 | 边界值分析 |
| TC046 | SQL注入攻击测试 | 1.在用户名输入"admin' OR '1'='1"<br>2.尝试登录<br>3.观察系统行为 | 系统正常处理，不发生SQL注入 | 高 | 边界值分析 |
| TC047 | 密码暴力破解防护 | 1.连续输入错误密码10次<br>2.观察系统响应<br>3.验证防护机制 | 账户被临时锁定或要求验证码 | 高 | 边界值分析 |
| TC048 | 会话超时测试 | 1.登录系统<br>2.长时间不操作(30分钟)<br>3.尝试访问需要登录的页面 | 会话超时，要求重新登录 | 中 | 边界值分析 |
| TC049 | 跨站脚本攻击防护 | 1.在个人信息中输入"<script>alert('XSS')</script>"<br>2.保存信息<br>3.查看页面显示 | 脚本被过滤，不执行恶意代码 | 高 | 边界值分析 |
| TC050 | 无效邮箱格式 | 1.输入"invalid-email"<br>2.尝试注册<br>3.查看验证结果 | 提示"请输入正确的邮箱格式" | 中 | 边界值分析 |

## 3. 测试用例执行计划

### 3.1 测试轮次安排
- **第一轮**: 执行高优先级用例(TC001-TC015, TC031-TC047)
- **第二轮**: 执行中优先级用例(TC016-TC030, TC048-TC050)  
- **第三轮**: 执行低优先级用例和回归测试

### 3.2 测试环境配置
- **浏览器**: Chrome 90+, Firefox 85+, Safari 14+
- **操作系统**: Windows 10, macOS 11, iOS 14, Android 10
- **网络环境**: 4G/WiFi网络，模拟弱网环境

### 3.3 测试数据准备
- 准备有效/无效的手机号、邮箱地址
- 准备测试用的股票代码、基金代码
- 准备SQL注入、XSS攻击测试数据

## 4. 测试方法说明

### 4.1 等价类划分法
将输入数据分为有效等价类和无效等价类，每个等价类选择代表性测试数据。

### 4.2 边界值分析法  
重点测试输入域边界上的值，包括最小值、最大值、刚好超出边界的值。

## 5. 缺陷管理

### 5.1 缺陷等级定义
- **严重**: 系统崩溃、数据丢失、安全漏洞
- **重要**: 主要功能无法使用、性能严重下降
- **一般**: 次要功能异常、界面显示问题
- **轻微**: 文字错误、界面美观问题

### 5.2 缺陷跟踪流程
1. 发现缺陷 → 2. 记录缺陷 → 3. 分配处理 → 4. 修复验证 → 5. 关闭缺陷

## 6. 测试完成标准

- 所有高优先级测试用例执行完成，通过率≥95%
- 所有严重和重要缺陷已修复并验证通过
- 系统性能指标满足需求要求
- 兼容性测试覆盖主流浏览器和设备

---

**文档版本**: V1.0  
**编制日期**: 2025年6月2日  
**编制人**: 测试团队  
**审核人**: 测试经理
