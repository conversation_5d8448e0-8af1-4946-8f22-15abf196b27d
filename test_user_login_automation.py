#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新浪财经用户登录功能自动化测试
测试目标：用户注册登录功能
对应需求：R011_用户_注册登录
"""

import unittest
import time
import random
import string
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.keys import Keys


class UserLoginAutomationTest(unittest.TestCase):
    """用户登录功能自动化测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化设置"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        cls.driver = webdriver.Chrome(options=chrome_options)
        cls.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        cls.driver.implicitly_wait(10)
        cls.wait = WebDriverWait(cls.driver, 15)
        cls.base_url = "https://finance.sina.com.cn/"
        
        # 测试数据
        cls.test_data = {
            'valid_email': '<EMAIL>',
            'valid_password': 'Test123456',
            'invalid_email': 'invalid_email',
            'invalid_password': '123',
            'empty_field': ''
        }
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        if cls.driver:
            cls.driver.quit()
    
    def setUp(self):
        """每个测试用例前的设置"""
        self.driver.get(self.base_url)
        time.sleep(2)
    
    def generate_random_email(self):
        """生成随机邮箱地址"""
        random_string = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        return f"test_{random_string}@example.com"
    
    def find_login_entry(self):
        """查找登录入口"""
        login_selectors = [
            "//a[contains(text(), '登录')]",
            "//a[contains(text(), '登陆')]", 
            "//a[contains(text(), 'Login')]",
            "//a[contains(@href, 'login')]",
            "//button[contains(text(), '登录')]",
            "//*[@class='login' or @id='login']"
        ]
        
        for selector in login_selectors:
            try:
                login_element = self.driver.find_element(By.XPATH, selector)
                if login_element.is_displayed():
                    return login_element
            except NoSuchElementException:
                continue
        return None
    
    def test_01_login_page_access(self):
        """
        测试用例1：登录页面访问测试
        测试步骤：
        1. 访问新浪财经首页
        2. 查找登录入口
        3. 点击登录链接
        4. 验证登录页面元素
        """
        print("\n=== 执行测试用例1：登录页面访问测试 ===")
        
        try:
            # 查找登录入口
            login_entry = self.find_login_entry()
            
            if login_entry:
                print(f"✓ 找到登录入口: {login_entry.text}")
                login_entry.click()
                time.sleep(3)
            else:
                print("未找到登录入口，尝试直接访问登录页面")
                # 尝试常见的登录页面URL
                login_urls = [
                    "https://login.sina.com.cn/",
                    "https://passport.sina.com.cn/",
                    "https://finance.sina.com.cn/login/"
                ]
                
                for url in login_urls:
                    try:
                        self.driver.get(url)
                        time.sleep(2)
                        if "登录" in self.driver.title or "login" in self.driver.current_url.lower():
                            print(f"✓ 成功访问登录页面: {url}")
                            break
                    except Exception:
                        continue
            
            # 验证登录页面元素
            login_form_selectors = [
                "//form[contains(@class, 'login')]",
                "//div[contains(@class, 'login')]",
                "//input[@type='email' or @type='text']",
                "//input[@type='password']"
            ]
            
            login_form_found = False
            for selector in login_form_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        print(f"✓ 找到登录表单元素: {len(elements)}个")
                        login_form_found = True
                        break
                except Exception:
                    continue
            
            # 检查页面内容
            page_content = self.driver.page_source.lower()
            login_keywords = ['登录', 'login', '用户名', 'username', '密码', 'password']
            keyword_found = any(keyword in page_content for keyword in login_keywords)
            
            # 验证登录页面访问成功
            access_success = login_form_found or keyword_found or "login" in self.driver.current_url.lower()
            self.assertTrue(access_success, "应该能够访问登录页面")
            
            print("✓ 登录页面访问测试通过")
            
        except Exception as e:
            print(f"✗ 登录页面访问测试失败: {str(e)}")
            self.fail(f"登录页面访问测试失败: {str(e)}")
    
    def test_02_invalid_login_attempt(self):
        """
        测试用例2：无效登录尝试测试
        测试步骤：
        1. 访问登录页面
        2. 输入无效的邮箱格式
        3. 输入弱密码
        4. 尝试登录
        5. 验证错误提示
        """
        print("\n=== 执行测试用例2：无效登录尝试测试 ===")
        
        try:
            # 访问登录页面
            login_entry = self.find_login_entry()
            if login_entry:
                login_entry.click()
                time.sleep(3)
            else:
                self.driver.get("https://login.sina.com.cn/")
                time.sleep(2)
            
            # 查找用户名/邮箱输入框
            username_selectors = [
                "//input[@type='email']",
                "//input[@type='text' and (@name='username' or @name='email' or @placeholder='邮箱')]",
                "//input[contains(@placeholder, '用户名') or contains(@placeholder, '邮箱')]",
                "//input[@name='username' or @name='email']"
            ]
            
            username_input = None
            for selector in username_selectors:
                try:
                    username_input = self.driver.find_element(By.XPATH, selector)
                    if username_input.is_displayed():
                        break
                except NoSuchElementException:
                    continue
            
            # 查找密码输入框
            password_selectors = [
                "//input[@type='password']",
                "//input[@name='password']"
            ]
            
            password_input = None
            for selector in password_selectors:
                try:
                    password_input = self.driver.find_element(By.XPATH, selector)
                    if password_input.is_displayed():
                        break
                except NoSuchElementException:
                    continue
            
            if username_input and password_input:
                print("✓ 找到登录表单输入框")
                
                # 测试无效邮箱格式
                username_input.clear()
                username_input.send_keys(self.test_data['invalid_email'])
                password_input.clear()
                password_input.send_keys(self.test_data['invalid_password'])
                
                print(f"输入无效邮箱: {self.test_data['invalid_email']}")
                print(f"输入弱密码: {self.test_data['invalid_password']}")
                
                # 查找登录按钮
                login_button_selectors = [
                    "//button[@type='submit']",
                    "//input[@type='submit']",
                    "//button[contains(text(), '登录')]",
                    "//a[contains(text(), '登录')]"
                ]
                
                login_button = None
                for selector in login_button_selectors:
                    try:
                        login_button = self.driver.find_element(By.XPATH, selector)
                        if login_button.is_displayed():
                            break
                    except NoSuchElementException:
                        continue
                
                if login_button:
                    login_button.click()
                    print("✓ 点击登录按钮")
                else:
                    # 尝试按回车键
                    password_input.send_keys(Keys.RETURN)
                    print("✓ 按回车键尝试登录")
                
                time.sleep(3)
                
                # 验证错误提示
                error_selectors = [
                    "//*[contains(text(), '错误') or contains(text(), '失败')]",
                    "//*[contains(text(), '格式') or contains(text(), '无效')]",
                    "//*[contains(@class, 'error') or contains(@class, 'warning')]",
                    "//*[contains(text(), '请输入') or contains(text(), '不能为空')]"
                ]
                
                error_found = False
                for selector in error_selectors:
                    try:
                        error_elements = self.driver.find_elements(By.XPATH, selector)
                        if error_elements:
                            for element in error_elements:
                                if element.is_displayed() and element.text.strip():
                                    print(f"✓ 找到错误提示: {element.text}")
                                    error_found = True
                                    break
                    except Exception:
                        continue
                
                # 检查是否仍在登录页面（未成功登录）
                current_url = self.driver.current_url
                still_on_login = "login" in current_url.lower() or "passport" in current_url.lower()
                
                # 验证无效登录被正确处理
                invalid_login_handled = error_found or still_on_login
                self.assertTrue(invalid_login_handled, "无效登录应该被正确处理并显示错误提示")
                
                print("✓ 无效登录尝试测试通过")
            else:
                print("未找到登录表单，跳过此测试")
                self.skipTest("未找到登录表单")
                
        except Exception as e:
            print(f"✗ 无效登录尝试测试失败: {str(e)}")
            self.fail(f"无效登录尝试测试失败: {str(e)}")
    
    def test_03_empty_fields_validation(self):
        """
        测试用例3：空字段验证测试
        测试步骤：
        1. 访问登录页面
        2. 保持用户名和密码为空
        3. 尝试登录
        4. 验证必填字段提示
        """
        print("\n=== 执行测试用例3：空字段验证测试 ===")
        
        try:
            # 访问登录页面
            login_entry = self.find_login_entry()
            if login_entry:
                login_entry.click()
                time.sleep(3)
            else:
                self.driver.get("https://login.sina.com.cn/")
                time.sleep(2)
            
            # 查找登录按钮并直接点击（不输入任何内容）
            login_button_selectors = [
                "//button[@type='submit']",
                "//input[@type='submit']", 
                "//button[contains(text(), '登录')]",
                "//a[contains(text(), '登录')]"
            ]
            
            login_button = None
            for selector in login_button_selectors:
                try:
                    login_button = self.driver.find_element(By.XPATH, selector)
                    if login_button.is_displayed():
                        break
                except NoSuchElementException:
                    continue
            
            if login_button:
                print("✓ 找到登录按钮，尝试空字段登录")
                login_button.click()
                time.sleep(2)
                
                # 验证必填字段提示
                validation_selectors = [
                    "//*[contains(text(), '请输入') or contains(text(), '不能为空')]",
                    "//*[contains(text(), '必填') or contains(text(), '必须')]",
                    "//*[contains(@class, 'required') or contains(@class, 'error')]",
                    "//input[@required]"  # HTML5 required属性
                ]
                
                validation_found = False
                for selector in validation_selectors:
                    try:
                        elements = self.driver.find_elements(By.XPATH, selector)
                        if elements:
                            for element in elements:
                                if element.is_displayed():
                                    if element.tag_name == 'input':
                                        print("✓ 找到必填字段标记")
                                    else:
                                        print(f"✓ 找到验证提示: {element.text}")
                                    validation_found = True
                                    break
                    except Exception:
                        continue
                
                # 检查浏览器原生验证
                try:
                    # 执行JavaScript检查表单验证状态
                    validation_message = self.driver.execute_script("""
                        var inputs = document.querySelectorAll('input[required]');
                        for (var i = 0; i < inputs.length; i++) {
                            if (inputs[i].validationMessage) {
                                return inputs[i].validationMessage;
                            }
                        }
                        return null;
                    """)
                    
                    if validation_message:
                        print(f"✓ 浏览器原生验证提示: {validation_message}")
                        validation_found = True
                        
                except Exception:
                    pass
                
                # 检查是否仍在登录页面
                current_url = self.driver.current_url
                still_on_login = "login" in current_url.lower() or "passport" in current_url.lower()
                
                # 验证空字段验证正常工作
                empty_validation_works = validation_found or still_on_login
                self.assertTrue(empty_validation_works, "空字段应该触发验证提示")
                
                print("✓ 空字段验证测试通过")
            else:
                print("未找到登录按钮，跳过此测试")
                self.skipTest("未找到登录按钮")
                
        except Exception as e:
            print(f"✗ 空字段验证测试失败: {str(e)}")
            self.fail(f"空字段验证测试失败: {str(e)}")


def run_login_tests():
    """运行登录相关测试用例"""
    print("=" * 60)
    print("新浪财经用户登录功能自动化测试开始")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(UserLoginAutomationTest)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("\n" + "=" * 60)
    print("登录测试结果汇总:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print("=" * 60)
    
    return result


if __name__ == "__main__":
    # 运行测试
    test_result = run_login_tests()
    
    # 根据测试结果设置退出码
    if test_result.failures or test_result.errors:
        exit(1)
    else:
        exit(0)
