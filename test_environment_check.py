#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境检查测试 - 验证自动化测试环境是否正常
"""

import unittest
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC


class EnvironmentCheckTest(unittest.TestCase):
    """环境检查测试类"""
    
    def setUp(self):
        """测试前设置"""
        print("\n正在启动Chrome浏览器...")
        
        # Chrome配置
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1280,720')
        
        # 如果需要无头模式，取消下面注释
        # chrome_options.add_argument('--headless')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            self.wait = WebDriverWait(self.driver, 15)
            print("✓ Chrome浏览器启动成功")
        except Exception as e:
            print(f"✗ Chrome浏览器启动失败: {str(e)}")
            raise
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'driver'):
            self.driver.quit()
            print("✓ 浏览器已关闭")
    
    def test_environment_setup(self):
        """测试环境设置检查"""
        print("\n=== 环境检查测试 ===")
        
        try:
            # 测试访问百度（国内网络稳定）
            print("正在访问百度...")
            self.driver.get("https://www.baidu.com")
            time.sleep(2)
            
            # 验证页面标题
            title = self.driver.title
            print(f"页面标题: {title}")
            self.assertIn("百度", title, "应该能够访问百度首页")
            
            # 查找搜索框
            search_box = self.driver.find_element(By.ID, "kw")
            self.assertTrue(search_box.is_displayed(), "搜索框应该可见")
            print("✓ 找到搜索框")
            
            # 测试输入
            search_box.send_keys("selenium测试")
            print("✓ 输入测试成功")
            
            # 查找搜索按钮
            search_button = self.driver.find_element(By.ID, "su")
            self.assertTrue(search_button.is_displayed(), "搜索按钮应该可见")
            print("✓ 找到搜索按钮")
            
            print("✓ 环境检查测试通过")
            
        except Exception as e:
            print(f"✗ 环境检查测试失败: {str(e)}")
            raise
    
    def test_sina_finance_access(self):
        """测试新浪财经网站访问"""
        print("\n=== 新浪财经访问测试 ===")
        
        try:
            print("正在访问新浪财经...")
            self.driver.get("https://finance.sina.com.cn/")
            time.sleep(3)
            
            # 验证页面加载
            title = self.driver.title
            print(f"页面标题: {title}")
            
            # 检查页面是否包含财经相关内容
            page_source = self.driver.page_source.lower()
            finance_keywords = ['财经', '股票', '基金', '新浪']
            
            keyword_found = False
            for keyword in finance_keywords:
                if keyword in page_source:
                    print(f"✓ 页面包含关键词: {keyword}")
                    keyword_found = True
                    break
            
            self.assertTrue(keyword_found, "页面应该包含财经相关内容")
            
            # 检查页面大小
            page_size = len(page_source)
            print(f"页面内容大小: {page_size} 字符")
            self.assertGreater(page_size, 1000, "页面内容应该足够丰富")
            
            print("✓ 新浪财经访问测试通过")
            
        except Exception as e:
            print(f"✗ 新浪财经访问测试失败: {str(e)}")
            print("这可能是网络问题，请检查网络连接")
            # 不抛出异常，因为网络问题不应该影响环境检查


def run_environment_check():
    """运行环境检查"""
    print("=" * 60)
    print("自动化测试环境检查")
    print("=" * 60)
    
    # 检查Python版本
    import sys
    print(f"Python版本: {sys.version}")
    
    # 检查Selenium版本
    try:
        import selenium
        print(f"Selenium版本: {selenium.__version__}")
    except ImportError:
        print("✗ Selenium未安装")
        return False
    
    # 运行测试
    suite = unittest.TestLoader().loadTestsFromTestCase(EnvironmentCheckTest)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    print("环境检查结果:")
    print(f"测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures or result.errors:
        print("\n问题详情:")
        for test, error in result.failures + result.errors:
            print(f"- {test}: {error.split('AssertionError:')[-1].strip() if 'AssertionError:' in error else error}")
    
    print("=" * 60)
    
    return len(result.failures) == 0 and len(result.errors) == 0


if __name__ == "__main__":
    success = run_environment_check()
    if success:
        print("\n🎉 环境检查通过！可以运行自动化测试了。")
        print("\n下一步：运行主要测试")
        print("python run_all_tests.py")
    else:
        print("\n❌ 环境检查失败，请解决上述问题后重试。")
    
    exit(0 if success else 1)
